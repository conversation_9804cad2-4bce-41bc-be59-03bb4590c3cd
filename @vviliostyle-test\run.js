const fs = require('fs-extra'); // Use fs-extra for easy directory copying
const path = require('path');
const { execSync } = require('child_process');

const VIVLIOSTYLE_BIN = 'vivliostyle';
const INPUT_MD = 'input.md';
const STYLE_CSS = 'vivliostyle-style.css';
const INTERMEDIATE_HTML = 'intermediate.html';
const FINAL_MD = 'final.md';
const OUTPUT_PDF = 'output.pdf';

function buildStep(command, description) {
    console.log(`\n--- ${description} ---`);
    try {
        execSync(command, { stdio: 'inherit' });
        console.log(`--- Success ---`);
    } catch (error) {
        console.error(`--- Error: ${description} failed ---`);
        process.exit(1);
    }
}

// --- Pre-build Step: Ensure assets are available ---
console.log('--- Copying asset directories ---');
const ASSET_DIRS = ['img', 'icon'];
ASSET_DIRS.forEach(dir => {
    const sourceDir = path.join('..', dir);
    const destDir = path.join('.', dir); // Copy to current dir for final build
    const intermediateDestDir = path.join(INTERMEDIATE_HTML, dir); // Copy to intermediate for first pass

    if (fs.existsSync(sourceDir)) {
        fs.copySync(sourceDir, destDir, { overwrite: true });
        fs.ensureDirSync(INTERMEDIATE_HTML); // Ensure intermediate dir exists
        fs.copySync(sourceDir, intermediateDestDir, { overwrite: true });
        console.log(`Copied '${sourceDir}' to '${destDir}' and '${intermediateDestDir}'`);
    } else {
        console.warn(`Warning: Asset directory '${sourceDir}' not found.`);
    }
});


// 1. Create a clean input file (based on the original)
const originalContent = fs.readFileSync('../vivliostyle-test-dynamic.md', 'utf-8');
// Remove the faulty, duplicated header/footer sections from the original file
const firstHeadingIndex = originalContent.indexOf('# ');
const cleanedContent = firstHeadingIndex >= 0 ? originalContent.substring(firstHeadingIndex) : originalContent;
fs.writeFileSync(INPUT_MD, cleanedContent);
console.log('--- Created cleaned input file: input.md ---');


// 2. First Pass: Build HTML to identify page structure
buildStep(
    `${VIVLIOSTYLE_BIN} build ${INPUT_MD} --style ${STYLE_CSS} -o ${INTERMEDIATE_HTML}`,
    'First Pass: Building intermediate HTML'
);

// 3. Analyze and Inject Navigation
console.log('\n--- Analyzing HTML and injecting navigation ---');
const intermediateHtmlPath = path.join(INTERMEDIATE_HTML, 'input.html');
const htmlContent = fs.readFileSync(intermediateHtmlPath, 'utf-8');
const { JSDOM } = require('jsdom');
const dom = new JSDOM(htmlContent);
const document = dom.window.document;

// Find all section IDs which represent our pages
const pages = Array.from(document.querySelectorAll('section')).map(s => {
    const heading = s.querySelector('h1, h2, h3, h4, h5, h6');
    return heading ? heading.id : null;
}).filter(id => id);
if (pages.length === 0) {
    console.error("--- Error: No sections with IDs found. Cannot generate navigation. ---");
    process.exit(1);
}
console.log(`Found pages: ${pages.join(', ')}`);

const contentsId = pages.find(p => p.includes('contents')) || pages[0];

// Generate table of contents with proper hierarchy and numbering
function generateTableOfContents() {
    let tocContent = '# 目 录 {#contents}\n\n';
    const headings = document.querySelectorAll('h2, h3');
    let h2Counter = 0;
    let h3Counter = 0;
    let currentPageNumber = 3; // Start from page 3 (after title and contents)

    headings.forEach(heading => {
        if (heading.id) {
            const title = heading.textContent.replace(/ \{#.*\}/, '').trim();

            if (heading.tagName === 'H2') {
                h2Counter++;
                h3Counter = 0; // Reset sub-counter for new H2 section
                tocContent += `- [${h2Counter}. ${title}](#${heading.id})<span>${currentPageNumber}</span>\n`;
                currentPageNumber++; // Each H2 gets its own page
            } else if (heading.tagName === 'H3') {
                h3Counter++;
                // Indent H3 entries and use hierarchical numbering
                tocContent += `  - [${h2Counter}.${h3Counter} ${title}](#${heading.id})<span>${currentPageNumber}</span>\n`;
                // H3 sections might be on the same page as H2 or next page
                if (h3Counter === 1) {
                    // First H3 might be on same page as H2
                } else {
                    currentPageNumber++; // Subsequent H3s get new pages
                }
            }
        }
    });
    return tocContent;
}

function generateNavbar(currentIndex, currentTitle = '') {
    const prevId = pages[Math.max(0, currentIndex - 1)];
    const nextId = pages[Math.min(pages.length - 1, currentIndex + 1)];
    const firstId = pages[0];
    const lastId = pages[pages.length - 1];

    // Restore the header-line div, which is the reliable hook for CSS
    return `
<div class="footer-content">
    <div class="footer-text">
        <span class="running-title"></span>
        <span><span class="page-number"></span></span>
    </div>
    <div class="navigation-bar">
        <a href="#${firstId}" class="nav-btn first" title="First Page">First</a>
        <a href="#${prevId}" class="nav-btn prev" title="Previous Page">Previous</a>
        <a href="#${contentsId}" class="nav-btn contents" title="Contents">Contents</a>
        <a href="#${nextId}" class="nav-btn next" title="Next Page">Next</a>
        <a href="#${lastId}" class="nav-btn last" title="Last Page">Last</a>
    </div>
</div>
<div class="header-logo">UIS LOGO</div>
<div class="header-line"></div>
`;
}

const sections = cleanedContent.split('---');
let finalMdContent = '';
let h2Counter = 0;

sections.forEach((section, index) => {
    let trimmedSection = section.trim();
    if (trimmedSection) {
        
        // Auto-number main content sections (H2s)
        const h2Match = trimmedSection.match(/^##\s+(.*)/m);
        if (h2Match) {
            h2Counter++;
            const title = h2Match[1].replace(/\{#.*\}$/, '').trim();
            trimmedSection = trimmedSection.replace(
                /^(##\s+)(.*)$/m,
                `$1${h2Counter}. ${title} {.running-head}`
            );
        }

        // Replace contents section with the newly numbered TOC
        if (trimmedSection.includes('#contents')) {
            trimmedSection = generateTableOfContents();
        }
        
        finalMdContent += trimmedSection + '\n\n';
        
        const navBar = generateNavbar(index);
        finalMdContent += navBar + '\n\n---\n\n';
    }
});

fs.writeFileSync(FINAL_MD, finalMdContent);
console.log('--- Created final markdown with injected navigation: final.md ---');


// 4. Second Pass: Build the final PDF
buildStep(
    `${VIVLIOSTYLE_BIN} build ${FINAL_MD} --style ${STYLE_CSS} -o ${OUTPUT_PDF}`,
    'Second Pass: Building final PDF'
);

console.log(`\n\n✅ Build complete! Output at: ${OUTPUT_PDF}`);
