/* Vivliostyle CSS for PDF navigation */
@page {
    size: A4;
    margin: 20mm 15mm 25mm 15mm;
    
    /* Combined footer with background spanning full width */
    @bottom-center {
        content: element(footer-content);
        background-color: #CDD7EE;
        border-top: 0.5pt solid #DADCE0;
        padding: 3mm 15mm;
        margin: 0 -15mm;
        height: 18mm;
        width: 210mm;
        box-sizing: border-box;
    }
    
    /* Header with logo */
    @top-right {
        content: element(header-logo);
        background-color: #0032AA;
        background-image: url('./icon/114504.png');
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        width: 55mm;
        height: 13mm;
        margin: -20mm -15mm 0 0;
    }
}

/* Colors */
:root {
    --uis-blue: #3464BF;
    --text-gray: #5F6368;
    --nav-strip: #CDD7EE;
    --header-bg: #0032AA;
}

/* Body */
body {
    font-family: "Times New Roman", serif;
    line-height: 1.4;
    color: #333;
}

/* Combined footer content */
.footer-content {
    position: running(footer-content);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 2mm 0;
}

/* Navigation behavior for PDF */
.nav-btn[href] {
    prince-link: auto;
}

.footer-text {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 0;
    font-size: 10pt;
    color: #5F6368;
}

.page-number::before {
    content: counter(page) "/" counter(pages);
}

.navigation-bar {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 3mm;
    margin-top: 2mm;
}

/* PDF bookmark/outline support */
.navigation-bar a {
    prince-bookmark-level: none;
}

.nav-btn {
    display: inline-block;
    height: 8mm;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    border: none;
    cursor: pointer;
    text-decoration: none;
    color: transparent;
    font-size: 0;
    margin: 0;
    padding: 0;
}

.nav-btn.first { 
    background-image: url('./icon/first_page.png'); 
    width: 12mm;
}
.nav-btn.prev { 
    background-image: url('./icon/chevron_left.png'); 
    width: 12mm;
}
.nav-btn.contents { 
    background-image: url('./icon/contents_page.png'); 
    width: 24mm;
}
.nav-btn.next { 
    background-image: url('./icon/chevron_right.png'); 
    width: 12mm;
}
.nav-btn.last { 
    background-image: url('./icon/last_page.png'); 
    width: 12mm;
}

/* Header logo */
.header-logo {
    position: running(header-logo);
    color: transparent;
    font-size: 0;
}

/* Title page */
h1:first-of-type {
    color: var(--uis-blue);
    font-size: 32pt;
    font-weight: bold;
    text-align: center;
    margin: 50mm 0 20mm 0;
    page-break-before: auto;
}

/* Subtitle */
h1:first-of-type + p {
    color: var(--text-gray);
    font-size: 20pt;
    font-weight: bold;
    text-align: center;
    margin-bottom: 40mm;
}

/* Contents heading */
h1:nth-of-type(2) {
    color: var(--uis-blue);
    font-size: 24pt;
    text-align: center;
    page-break-before: always;
    margin: 20mm 0;
}

/* Section headings */
h2 {
    color: var(--uis-blue);
    font-size: 18pt;
    font-weight: bold;
    page-break-before: always;
    margin: 20mm 0 10mm 0;
}

/* Page breaks */
hr {
    page-break-after: always;
    border: none;
    height: 0;
    margin: 0;
}

/* Contents list styling */
ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

li {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin: 3mm 0;
    position: relative;
    font-size: 14pt;
    line-height: 1.5;
    overflow: hidden;
}

/* Main list items with dotted line */
li::after {
    content: "";
    flex: 1;
    height: 1px;
    background: repeating-linear-gradient(
        to right,
        transparent 0,
        transparent 1px,
        #666 1px,
        #666 3px,
        transparent 3px,
        transparent 4px
    );
    margin: 0 5mm;
    align-self: baseline;
    position: relative;
    top: 0.7em;
}

/* Nested list items (sub-items) */
li ul {
    margin: 2mm 0 0 0;
    padding: 0;
    width: 100%;
}

li ul li {
    margin: 1mm 0;
    font-size: 13pt;
    padding-left: 15mm;
}

/* Remove dotted line from parent li when it has nested ul */
li:has(ul)::after {
    display: none;
}

li a {
    color: #333;
    text-decoration: none;
    font-weight: normal;
    white-space: nowrap;
}

/* Contents page numbers */
li span {
    font-weight: bold;
    color: #333;
    white-space: nowrap;
}

/* Links */
a {
    color: var(--uis-blue);
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}