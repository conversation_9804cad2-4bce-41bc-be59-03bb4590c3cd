/* Vivliostyle CSS for PDF navigation */
@page {
    size: A4;
    margin: 20mm 15mm 25mm 15mm;
    
    @bottom-center {
        content: element(footer-content);
        background-color: #CDD7EE;
        border-top: 0.5pt solid #DADCE0;
        padding: 2mm 15mm;
        margin: 5mm -15mm -70mm -15mm;
        height: 15mm;
        width: 210mm;
        box-sizing: border-box;
    }
    
    @top-left {
        content: element(header-line);
        margin: 6mm 0 0 -15mm; /* Position the line - MOVED UP */
    }
    
    @top-right {
        content: element(header-logo);
        margin: -5mm -15mm 0 0;
    }
}

/* Colors */
:root {
    --uis-blue: #3464BF;
    --text-gray: #5F6368;
    --nav-strip: #CDD7EE;
    --header-bg: #0032AA;
}

/* Body */
body {
    font-family: "Times New Roman", "SimSun", serif;
    line-height: 1.6; /* Increased line spacing */
    color: #333;
    font-size: 14pt; /* Set font size to Four, which is 14pt */
    text-align: justify; /* Justify text alignment */
}

/* --- RELIABLE HEADER ELEMENTS --- */
.header-line {
    position: running(header-line);
    height: 5pt;
    background-color: var(--header-bg);
    width: 210mm;
}

.header-logo {
    position: running(header-logo);
    background-color: var(--header-bg);
    background-image: url('./icon/114504.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    width: 50mm;
    height: 15mm;
    color: transparent; /* Hide the "UIS LOGO" text */
}
/* --- END RELIABLE HEADER ELEMENTS --- */


/* Combined footer content */
.footer-content {
    position: running(footer-content);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0;
    margin-top: 1mm;
}

/* Navigation behavior for PDF */
.nav-btn[href] {
    prince-link: auto;
}

.footer-text {
    display: flex;
    justify-content: space-between;
    width: 180mm; /* Exactly match the main content width (210mm - 15mm - 15mm) */
    font-size: 12pt;
    color: #5F6368;
}

.page-number::before {
    content: counter(page) "/" counter(pages);
}

.running-title {
    content: string(title, first);
}

.running-head {
    string-set: title content(text);
}

.navigation-bar {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 0mm;
}

.nav-btn {
    margin-left: -8mm;
}

.nav-btn:first-child {
    margin-left: 0;
}

/* PDF bookmark/outline support */
.navigation-bar a {
    prince-bookmark-level: none;
}

.nav-btn {
    display: inline-block;
    height: 12mm;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    border: none;
    cursor: pointer;
    text-decoration: none;
    color: transparent;
    font-size: 0;
    margin: 0;
    margin-left: -4mm;
    padding: 0;
}

.nav-btn.first { 
    background-image: url('./icon/first_page.png'); 
    width: 16mm;
}
.nav-btn.prev { 
    background-image: url('./icon/chevron_left.png'); 
    width: 16mm;
}
.nav-btn.contents { 
    background-image: url('./icon/contents_page.png'); 
    width: 32mm;
}
.nav-btn.next { 
    background-image: url('./icon/chevron_right.png'); 
    width: 16mm;
}
.nav-btn.last { 
    background-image: url('./icon/last_page.png'); 
    width: 16mm;
}

/* Title page */
h1:first-of-type {
    color: var(--uis-blue);
    font-size: 32pt;
    font-weight: bold;
    text-align: center;
    margin: 50mm 0 20mm 0;
    page-break-before: auto;
}

/* Subtitle */
h1:first-of-type + p {
    color: var(--text-gray);
    font-size: 20pt;
    font-weight: bold;
    text-align: center;
    margin-bottom: 40mm;
}

/* Contents heading */
h1:nth-of-type(2) {
    color: var(--uis-blue);
    font-size: 28pt;
    font-weight: bold;
    text-align: left;
    text-transform: uppercase;
    letter-spacing: 1pt;
    page-break-before: always;
    margin: 0mm 0 15mm 0;
}

/* Section headings */
h2 {
    color: var(--uis-blue);
    font-size: 18pt;
    font-weight: bold;
    page-break-before: always;
    margin: 0mm 0 10mm 0;
}

/* Paragraph Styling */
p {
    text-indent: 2em !important; /* Force indent on all paragraphs */
}

/* Remove indent for the subtitle on the title page */
h1:first-of-type + p {
    text-indent: 0 !important;
}


/* Page breaks */
hr {
    page-break-after: always;
    border: none;
    height: 0;
    margin: 0;
}

/* Contents list styling */
ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

li {
    display: block;
    margin: 3mm 0;
    position: relative;
    font-size: 14pt;
    line-height: 1.5;
    overflow: hidden;
}

/* Nested list items (sub-items) */
li ul {
    margin: 0;
    padding: 0;
}

li ul li {
    margin: 1mm 0 1mm 15mm;
    font-size: 13pt;
}

li a {
    color: #333;
    text-decoration: none;
    font-weight: normal;
    position: relative;
    z-index: 1;
    background: white;
    padding-right: 2mm;
}

li::before {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0.3em;
    height: 1px;
    background: repeating-linear-gradient(
        to right,
        transparent 0,
        transparent 1px,
        #666 1px,
        #666 2px
    );
}

/* Contents page numbers */
li span {
    font-weight: bold;
    color: #333;
    position: absolute;
    right: 0;
    background: white;
    padding-left: 2mm;
    z-index: 1;
}

/* Links */
a {
    color: #333;
    text-decoration: none;
    font-weight: normal;
}

a:hover {
    text-decoration: underline;
}

/* --- Image Sizing --- */
figure {
    page-break-inside: avoid;
    text-align: center; /* Center images within their container */
}

img {
    max-width: 100%; /* Ensure images do not overflow the page content area */
    height: auto;   /* Maintain aspect ratio */
    display: block; /* Helps with centering and margins */
    margin-left: auto;
    margin-right: auto;
}



