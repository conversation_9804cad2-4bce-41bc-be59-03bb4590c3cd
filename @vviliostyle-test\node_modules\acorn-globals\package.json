{"name": "acorn-globals", "version": "7.0.1", "description": "Detect global variables in JavaScript using acorn", "keywords": ["ast", "variable", "name", "lexical", "scope", "local", "global", "implicit"], "files": ["index.js", "LICENSE"], "dependencies": {"acorn": "^8.1.0", "acorn-walk": "^8.0.2"}, "devDependencies": {"testit": "^3.1.0"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "https://github.com/ForbesLindesay/acorn-globals.git"}, "author": "ForbesLindesay", "license": "MIT"}